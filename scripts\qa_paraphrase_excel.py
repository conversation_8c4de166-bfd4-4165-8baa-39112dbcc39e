import argparse
import os
import sys
from typing import Dict, List, Tuple

# Lazy import to give clearer error if pandas/openpyxl are missing
try:
    import pandas as pd
except Exception as e:
    print("[ERROR] pandas not available. Please install pandas and openpyxl.")
    raise


QUESTION_TRIGGERS = [
    "什么", "为何", "为什么", "怎样", "怎么", "如何", "是否", "能否", "哪里", "哪儿", "哪", "哪个", "哪一", "多少", "多久", "谁", "何时"
]


def has_question_mark(text: str) -> bool:
    return any(ch in text for ch in ["?", "？"])


def is_question_like(text: str) -> bool:
    t = text.strip()
    if has_question_mark(t):
        return True
    return any(t.startswith(w) for w in QUESTION_TRIGGERS)


def strip_trailing_punct(text: str) -> str:
    t = str(text).strip()
    # remove trailing common punctuation
    while len(t) > 0 and t[-1] in ["?", "？", "。", ".", "！", "!", "；", ";", "，", ","]:
        t = t[:-1].rstrip()
    return t


def quote_cn(text: str) -> str:
    t = str(text)
    # Use Chinese quotes, but avoid double quoting if already present
    t_stripped = t.strip()
    if (t_stripped.startswith("“") and t_stripped.endswith("”")) or (
        t_stripped.startswith('"') and t_stripped.endswith('"')
    ):
        return t_stripped
    return f"“{t_stripped}”"


def gen_paraphrases(original: str) -> Tuple[str, str, str]:
    base = strip_trailing_punct(original)
    if not base:
        return ("", "", "")

    if is_question_like(base):
        # Keep semantics by only adding neutral leading phrases
        p1 = f"请问，{strip_trailing_punct(base)}？"
        p2 = f"能否告知：{strip_trailing_punct(base)}？"
        p3 = f"想了解：{strip_trailing_punct(base)}？"
    else:
        # Turn statements into standard QA-style questions (neutral task framing)
        q = quote_cn(base)
        p1 = f"请问，关于{q}，应如何处理？"
        p2 = f"针对{q}，应该怎么操作？"
        p3 = f"{q}需要如何解决？"
    return (p1, p2, p3)


def find_target_sheet(sheets: Dict[str, 'pd.DataFrame']) -> str:
    # Find the first sheet that contains both required columns
    for name, df in sheets.items():
        cols = set(map(str, df.columns))
        if "条目描述" in cols and "应答" in cols:
            return name
    # If not found, fallback to the first sheet
    return list(sheets.keys())[0]


def process_file(input_path: str, output_path: str) -> Dict[str, int]:
    # Load all sheets
    xls = pd.read_excel(input_path, sheet_name=None)
    target_sheet = find_target_sheet(xls)
    df = xls[target_sheet].copy()

    # Ensure target columns exist
    for col in ["改写问题1", "改写问题2", "改写问题3"]:
        if col not in df.columns:
            df[col] = ""

    processed = 0
    empty = 0

    # Iterate rows
    for idx, val in df["条目描述"].items():
        text = "" if pd.isna(val) else str(val).strip()
        if not text:
            empty += 1
            df.at[idx, "改写问题1"] = ""
            df.at[idx, "改写问题2"] = ""
            df.at[idx, "改写问题3"] = ""
            continue
        p1, p2, p3 = gen_paraphrases(text)
        df.at[idx, "改写问题1"] = p1
        df.at[idx, "改写问题2"] = p2
        df.at[idx, "改写问题3"] = p3
        processed += 1

    # Put back and save all sheets to output
    xls[target_sheet] = df
    with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
        for name, sdf in xls.items():
            sdf.to_excel(writer, sheet_name=name, index=False)

    return {"processed": processed, "empty": empty, "sheet": target_sheet}


def main():
    parser = argparse.ArgumentParser(description="Generate QA paraphrases for Excel column '条目描述' into 改写问题1/2/3.")
    parser.add_argument("--input", required=True, help="Path to input .xlsx")
    parser.add_argument("--output", required=True, help="Path to output .xlsx")
    args = parser.parse_args()

    input_path = args.input
    output_path = args.output

    if not os.path.exists(input_path):
        print(f"[ERROR] Input file not found: {input_path}")
        sys.exit(1)

    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    stats = process_file(input_path, output_path)
    print(f"[DONE] Sheet: {stats['sheet']} | Processed: {stats['processed']} | Empty: {stats['empty']}")
    print(f"[OUT] {output_path}")


if __name__ == "__main__":
    main()

