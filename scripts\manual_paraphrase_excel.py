import pandas as pd
import re
import os
import sys

def has_chinese(text):
    """检测文本是否包含中文字符"""
    return bool(re.search(r'[\u4e00-\u9fff]', str(text)))

def manual_paraphrase(original_text):
    """人工语义改写函数 - 根据原文内容生成3个等义改写"""
    text = str(original_text).strip()
    if not text or text == 'nan':
        return "", "", ""
    
    # 检测语言
    is_chinese = has_chinese(text)
    
    # 根据内容进行语义改写
    if text == "Introduction":
        return (
            "What is the introduction section?",
            "Can you provide an overview?", 
            "What does the introduction cover?"
        )
    
    elif "TWA has planned to deploy" in text:
        return (
            "What are TWA's deployment plans for the new long-haul transport system?",
            "Can you describe TWA's planned long-haul transport deployment?",
            "What is TWA's strategy for implementing the new long-haul transport?"
        )
    
    elif text == "General Requirements":
        return (
            "What are the general requirements?",
            "Can you list the basic requirements?",
            "What general specifications need to be met?"
        )
    
    elif "Being a turnkey RFP bidder" in text:
        return (
            "What should a turnkey RFP bidder offer?",
            "What are the expectations for turnkey RFP bidders?",
            "What must turnkey bidders provide in their RFP response?"
        )
    
    elif "Offered BoQ should be aligned" in text:
        return (
            "How should the Bill of Quantities be aligned with requirements?",
            "What alignment is needed between BoQ and requirements?",
            "How must the BoQ correspond to the stated requirements?"
        )
    
    # 对于其他文本，根据内容类型生成改写
    elif is_chinese:
        # 中文改写
        if "是什么" in text or "什么是" in text:
            return (
                f"请问{text}？",
                f"能否解释一下{text}？",
                f"关于{text}，有什么说明？"
            )
        else:
            return (
                f"关于"{text}"，请问有什么相关信息？",
                f"能否说明一下"{text}"的情况？",
                f"请介绍"{text}"的具体内容。"
            )
    else:
        # 英文改写
        if text.endswith("?"):
            # 如果是问句
            base = text.rstrip("?").strip()
            return (
                f"Could you explain {base}?",
                f"What information is available about {base}?",
                f"Can you provide details on {base}?"
            )
        else:
            # 如果是陈述句
            return (
                f"What does '{text}' refer to?",
                f"Can you explain '{text}'?",
                f"What information is available about '{text}'?"
            )

def process_excel_file(input_path, output_path):
    """处理Excel文件"""
    print(f"正在读取文件: {input_path}")
    
    # 读取Excel文件
    df = pd.read_excel(input_path)
    
    # 确保目标列存在且为字符串类型
    for col in ['改写问题1', '改写问题2', '改写问题3']:
        if col not in df.columns:
            df[col] = ""
        df[col] = df[col].astype(object)
    
    processed_count = 0
    empty_count = 0
    
    print("开始处理条目描述...")
    
    # 逐行处理
    for idx, row in df.iterrows():
        original = row['条目描述']
        
        if pd.isna(original) or str(original).strip() == "":
            empty_count += 1
            df.at[idx, '改写问题1'] = ""
            df.at[idx, '改写问题2'] = ""
            df.at[idx, '改写问题3'] = ""
            continue
        
        # 生成改写
        p1, p2, p3 = manual_paraphrase(original)
        
        # 写入结果
        df.at[idx, '改写问题1'] = p1
        df.at[idx, '改写问题2'] = p2
        df.at[idx, '改写问题3'] = p3
        
        processed_count += 1
        
        # 每处理100行显示进度
        if processed_count % 100 == 0:
            print(f"已处理 {processed_count} 行...")
    
    # 保存结果
    print(f"正在保存到: {output_path}")
    df.to_excel(output_path, index=False)
    
    print(f"处理完成！")
    print(f"- 处理行数: {processed_count}")
    print(f"- 空白行数: {empty_count}")
    print(f"- 输出文件: {output_path}")
    
    return processed_count, empty_count

if __name__ == "__main__":
    input_file = "需求/SOC智能应答/测试/巴基斯坦测试数据.xlsx"
    output_file = "需求/SOC智能应答/测试/巴基斯坦测试数据_改写版_manual.xlsx"
    
    if not os.path.exists(input_file):
        print(f"错误：找不到输入文件 {input_file}")
        sys.exit(1)
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    try:
        process_excel_file(input_file, output_file)
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        sys.exit(1)
