import argparse
import json
import os
import re
import sys
import time
from typing import Dict, List, Optional, Tuple

import pandas as pd

# Optional: OpenAI client (installed on demand)
try:
    import openai  # type: ignore
except Exception:
    openai = None  # Will check at runtime


CN_CHAR_PATTERN = re.compile(r"[\u4e00-\u9fff]")


def has_chinese(text: str) -> bool:
    return bool(CN_CHAR_PATTERN.search(text or ""))


def ensure_string_columns(df: pd.DataFrame, cols: List[str]) -> None:
    for col in cols:
        if col not in df.columns:
            df[col] = ""
        # Coerce to object to avoid dtype issues when assigning strings
        try:
            df[col] = df[col].astype(object)
        except Exception:
            pass


def find_target_sheet(sheets: Dict[str, pd.DataFrame]) -> str:
    for name, df in sheets.items():
        cols = set(map(str, df.columns))
        if "条目描述" in cols and "应答" in cols:
            return name
    # fallback to the first sheet
    return list(sheets.keys())[0]


def build_prompt(question: str, lang: str) -> Tuple[str, str]:
    # lang in {"zh", "en"}
    system = (
        "You are a professional paraphraser for retrieval-based QA. "
        "Your job is to generate three semantically faithful, diverse paraphrases of a user question for use in matching. "
        "Preserve all entities, numbers, time expressions, and domain-specific terms exactly. "
        "Do not add or remove constraints. Do not change the intent. One sentence per paraphrase. "
        "Avoid trivial changes (e.g., punctuation-only)."
    )

    if lang == "zh":
        user = (
            "请根据以下问题生成3个等义改写，以便用于检索匹配：\n"
            "- 保持语义完全一致，不引入或删除任何条件/约束/实体/数值/时间\n"
            "- 自然流畅，单句为宜，避免只改标点或微小字面变动\n"
            "- 仅使用中文输出\n"
            "请输出 JSON：{\"paraphrases\": [p1, p2, p3]}，其中每项为字符串。\n\n"
            f"原问题：{question}"
        )
    else:
        user = (
            "Please generate 3 semantically equivalent paraphrases of the question for retrieval:\n"
            "- Preserve all constraints/entities/numbers/times and the original intent\n"
            "- Natural and fluent; one sentence each; avoid trivial punctuation-only edits\n"
            "- Output strictly in English only\n"
            "Return JSON: {\"paraphrases\": [p1, p2, p3]} where each is a string.\n\n"
            f"Question: {question}"
        )
    return system, user


def parse_paraphrases(text: str) -> List[str]:
    # Try to locate JSON
    m = re.search(r"\{[\s\S]*\}", text)
    candidate = m.group(0) if m else text
    try:
        obj = json.loads(candidate)
        ps = obj.get("paraphrases", [])
        if isinstance(ps, list):
            ps = [str(x).strip() for x in ps if str(x).strip()]
            return ps[:3]
    except Exception:
        pass
    # Fallback: split lines
    lines = [ln.strip(" -\t\r\n") for ln in text.splitlines() if ln.strip()]
    return [ln for ln in lines[:3]]


def ensure_openai_client() -> None:
    global openai
    if openai is None:
        raise RuntimeError(
            "The 'openai' package is not installed. Please install it via 'pip install openai' and set OPENAI_API_KEY."
        )
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise RuntimeError("OPENAI_API_KEY is not set.")
    openai.api_key = api_key


def call_openai_chat(model: str, system_prompt: str, user_prompt: str, temperature: float = 0.7, max_retries: int = 3) -> str:
    ensure_openai_client()
    last_err: Optional[Exception] = None
    for attempt in range(max_retries):
        try:
            resp = openai.ChatCompletion.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
            )
            return resp["choices"][0]["message"]["content"]
        except Exception as e:
            last_err = e
            time.sleep(1.5 * (attempt + 1))
    raise RuntimeError(f"OpenAI call failed: {last_err}")


def paraphrase_three(question: str, model: str) -> List[str]:
    lang = "zh" if has_chinese(question) else "en"
    system, user = build_prompt(question, lang)
    raw = call_openai_chat(model=model, system_prompt=system, user_prompt=user)
    ps = parse_paraphrases(raw)
    # Post-filters
    if lang == "en":
        # Remove any paraphrase that contains Chinese characters
        ps = [p for p in ps if not has_chinese(p)]
    # Deduplicate while preserving order
    seen = set()
    uniq = []
    for p in ps:
        if p not in seen and p:
            seen.add(p)
            uniq.append(p)
    # Ensure exactly three entries (pad with empty strings if necessary)
    while len(uniq) < 3:
        uniq.append("")
    return uniq[:3]


def process_excel(input_path: str, output_path: str, sheet: Optional[str], limit: Optional[int], model: str) -> Dict[str, int]:
    xls = pd.read_excel(input_path, sheet_name=None)
    target_sheet = sheet or find_target_sheet(xls)
    df = xls[target_sheet].copy()

    ensure_string_columns(df, ["改写问题1", "改写问题2", "改写问题3"])  # ensure columns exist and are str-like

    processed = 0
    skipped = 0

    rows = df.index.tolist()
    if limit is not None:
        rows = rows[:limit]

    for idx in rows:
        val = df.at[idx, "条目描述"] if "条目描述" in df.columns else None
        text = "" if pd.isna(val) else str(val).strip()
        if not text:
            skipped += 1
            continue
        try:
            p1, p2, p3 = paraphrase_three(text, model)
            df.at[idx, "改写问题1"] = p1
            df.at[idx, "改写问题2"] = p2
            df.at[idx, "改写问题3"] = p3
            processed += 1
        except Exception as e:
            # On failure, leave as-is but count as skipped
            skipped += 1

    xls[target_sheet] = df
    with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
        for name, sdf in xls.items():
            sdf.to_excel(writer, sheet_name=name, index=False)

    return {"processed": processed, "skipped": skipped, "sheet": target_sheet}


def main():
    parser = argparse.ArgumentParser(description="LLM-based QA paraphrasing for Excel column '条目描述' -> 改写问题1/2/3")
    parser.add_argument("--input", required=True, help="Path to input .xlsx")
    parser.add_argument("--output", required=True, help="Path to output .xlsx")
    parser.add_argument("--sheet", required=False, help="Sheet name (optional)")
    parser.add_argument("--limit", type=int, default=None, help="Process only first N rows (pilot mode)")
    parser.add_argument("--model", type=str, default=os.getenv("OPENAI_MODEL", "gpt-4o-mini"), help="OpenAI Chat model name")
    args = parser.parse_args()

    if not os.path.exists(args.input):
        print(f"[ERROR] Input file not found: {args.input}")
        sys.exit(1)

    os.makedirs(os.path.dirname(args.output), exist_ok=True)

    stats = process_excel(args.input, args.output, sheet=args.sheet, limit=args.limit, model=args.model)
    print(f"[DONE] Sheet: {stats['sheet']} | Processed: {stats['processed']} | Skipped: {stats['skipped']}")
    print(f"[OUT] {args.output}")


if __name__ == "__main__":
    main()

