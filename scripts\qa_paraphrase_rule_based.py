import argparse
import os
import re
import sys
from typing import Dict, List, Optional, Tuple

import pandas as pd

CN_CHAR_PATTERN = re.compile(r"[\u4e00-\u9fff]")


def has_chinese(text: str) -> bool:
    return bool(CN_CHAR_PATTERN.search(text or ""))


def strip_trailing_q(text: str) -> str:
    t = str(text or "").strip()
    while len(t) > 0 and t[-1] in ["?", "？", "。", ".", "！", "!", "；", ";", "，", ","]:
        t = t[:-1].rstrip()
    return t

# ---------------- Chinese heuristics ---------------- #

ZH_PATTERNS = {
    "how": re.compile(r"^(如何|怎么|怎样|怎样才能|应当?如何|该如何|要如何)(.+)$"),
    "what": re.compile(r"^(什么是)(.+)$"),
    "means": re.compile(r"(.+?)(是什么意思|指的是什么)$"),
    "why": re.compile(r"^(为什么|为何)(.+)$"),
    "yesno": re.compile(r"(是否|能否|可以|可否|是不是|会不会)|吗[?？]?$"),
    "where": re.compile(r"(哪里|在哪儿|在哪|何处|位于哪|位于何处)"),
    "when": re.compile(r"(什么时候|何时|在何时)"),
    "who": re.compile(r"(谁|哪位|何人)"),
    "howmuch": re.compile(r"(多少|多大|多长|多久|多远)"),
}


def zh_paraphrase(q: str) -> List[str]:
    s = strip_trailing_q(q)
    # Try classify
    if ZH_PATTERNS["what"].match(s):
        obj = ZH_PATTERNS["what"].match(s).group(2).strip()
        return [
            f"{obj}的定义是什么？",
            f"什么是{obj}？",
            f"{obj}具体指什么？",
        ]
    m_means = ZH_PATTERNS["means"].match(s)
    if m_means:
        obj = m_means.group(1).strip()
        return [
            f"{obj}是什么意思？",
            f"{obj}具体指什么？",
            f"可以解释一下{obj}的含义吗？",
        ]
    m_how = ZH_PATTERNS["how"].match(s)
    if m_how:
        rest = m_how.group(2).strip()
        return [
            f"{rest}应该如何进行？",
            f"{rest}有哪些步骤？",
            f"{rest}需要怎么做？",
        ]
    m_why = ZH_PATTERNS["why"].match(s)
    if m_why:
        rest = m_why.group(2).strip()
        return [
            f"{rest}的原因是什么？",
            f"是什么导致了{rest}？",
            f"为何会{rest}？",
        ]
    if ZH_PATTERNS["where"].search(s):
        return [
            f"{s}的具体位置在哪里？",
            f"{s}位于何处？",
            f"{s}在什么位置？",
        ]
    if ZH_PATTERNS["when"].search(s):
        return [
            f"{s}的时间是什么时候？",
            f"{s}将在何时发生？",
            f"{s}预计何时进行？",
        ]
    if ZH_PATTERNS["who"].search(s):
        return [
            f"{s}由谁负责？",
            f"{s}的负责人是谁？",
            f"{s}是由哪位负责？",
        ]
    if ZH_PATTERNS["howmuch"].search(s):
        return [
            f"{s}的数值/范围是多少？",
            f"{s}具体是多少？",
            f"{s}大概为多少？",
        ]
    if ZH_PATTERNS["yesno"].search(s) or s.endswith("吗"):
        return [
            f"{s}是否可以？",
            f"能否{s}？",
            f"{s}可以吗？",
        ]
    # Default: treat as term/topic -> definition/description requests
    return [
        f"“{s}”是什么意思？",
        f"什么是“{s}”？",
        f"可以解释一下“{s}”的含义吗？",
    ]

# ---------------- English heuristics ---------------- #

EN_PATTERNS = {
    "how_to": re.compile(r"^\s*how\s+to\s+(.+?)[?]?\s*$", re.IGNORECASE),
    "how_do": re.compile(r"^\s*how\s+(do|can|should|would|could|did|does|will)\s+(i|we|one)\s+(.+?)[?]?\s*$", re.IGNORECASE),
    "what_is": re.compile(r"^\s*what\s+is\s+(.+?)[?]?\s*$", re.IGNORECASE),
    "what_mean": re.compile(r"^\s*what\s+does\s+(.+?)\s+mean[?]?\s*$", re.IGNORECASE),
    "why": re.compile(r"^\s*why\s+(.+)$", re.IGNORECASE),
    "where": re.compile(r"^\s*where\b(.+)$", re.IGNORECASE),
    "when": re.compile(r"^\s*when\b(.+)$", re.IGNORECASE),
    "who": re.compile(r"^\s*who\b(.+)$", re.IGNORECASE),
    "how_many": re.compile(r"^\s*how\s+(many|much|long|far)\b(.+)$", re.IGNORECASE),
    "yesno": re.compile(r"^\s*(is|are|am|do|does|did|can|could|will|would|should|has|have|had|may|might)\b.+[?]?\s*$", re.IGNORECASE),
}


def en_paraphrase(q: str) -> List[str]:
    s = strip_trailing_q(q)
    # Patterns with extraction for better natural rewrites
    m = EN_PATTERNS["how_to"].match(s)
    if m:
        action = m.group(1).strip()
        return [
            f"What are the steps to {action}?",
            f"How should I go about {action}?",
            f"What is the procedure for {action}?",
        ]
    m = EN_PATTERNS["how_do"].match(s)
    if m:
        action = m.group(3).strip()
        return [
            f"What are the steps to {action}?",
            f"How should we proceed to {action}?",
            f"What is the proper way to {action}?",
        ]
    m = EN_PATTERNS["what_is"].match(s)
    if m:
        subj = m.group(1).strip()
        return [
            f"What is the definition of {subj}?",
            f"What does {subj} refer to?",
            f"Could you explain {subj}?",
        ]
    m = EN_PATTERNS["what_mean"].match(s)
    if m:
        subj = m.group(1).strip()
        return [
            f"What does {subj} mean?",
            f"What does {subj} refer to?",
            f"Could you clarify the meaning of {subj}?",
        ]
    m = EN_PATTERNS["why"].match(s)
    if m:
        rest = m.group(1).strip()
        return [
            f"What is the reason for {rest}?",
            f"What causes {rest}?",
            f"Why does {rest}?",
        ]
    if EN_PATTERNS["where"].match(s):
        return [
            f"Where is {s[6:]}?" if s.lower().startswith("where ") else f"Where is {s}?",
            f"In which location is {s[6:]}?" if s.lower().startswith("where ") else f"In which location is {s}?",
            f"Where can I find {s[6:]}?" if s.lower().startswith("where ") else f"Where can I find {s}?",
        ]
    if EN_PATTERNS["when"].match(s):
        rest = s[5:].strip() if s.lower().startswith("when ") else s
        return [
            f"When does {rest}?",
            f"At what time does {rest}?",
            f"When is {rest} scheduled?",
        ]
    if EN_PATTERNS["who"].match(s):
        rest = s[4:].strip() if s.lower().startswith("who ") else s
        return [
            f"Who is responsible for {rest}?",
            f"Who handles {rest}?",
            f"Which person is in charge of {rest}?",
        ]
    if EN_PATTERNS["how_many"].match(s):
        rest = s
        return [
            f"{rest}?",
            f"Could you tell me {rest}?",
            f"Do you know {rest}?",
        ]
    if EN_PATTERNS["yesno"].match(s):
        return [
            f"{s}?",
            f"Is it possible that {s}?",
            f"Can we confirm whether {s}?",
        ]
    # Default: treat as term/topic
    return [
        f"What does '{s}' mean?",
        f"What is the definition of '{s}'?",
        f"Could you explain '{s}'?",
    ]

# ---------------- Processing Excel ---------------- #

def ensure_string_columns(df: pd.DataFrame, cols: List[str]) -> None:
    for col in cols:
        if col not in df.columns:
            df[col] = ""
        try:
            df[col] = df[col].astype(object)
        except Exception:
            pass


def find_target_sheet(sheets: Dict[str, pd.DataFrame]) -> str:
    for name, df in sheets.items():
        cols = set(map(str, df.columns))
        if "条目描述" in cols and "应答" in cols:
            return name
    return list(sheets.keys())[0]


def gen_paraphrases(original: str) -> Tuple[str, str, str]:
    base = strip_trailing_q(original)
    if not base:
        return ("", "", "")
    if has_chinese(base):
        ps = zh_paraphrase(base)
    else:
        ps = en_paraphrase(base)
    # De-duplicate and ensure 3
    seen = set()
    uniq: List[str] = []
    for p in ps:
        p = p.strip()
        if not p:
            continue
        if p not in seen:
            seen.add(p)
            uniq.append(p)
    while len(uniq) < 3:
        uniq.append("")
    return (uniq[0], uniq[1], uniq[2])


def process_file(input_path: str, output_path: str) -> Dict[str, int]:
    xls = pd.read_excel(input_path, sheet_name=None)
    target_sheet = find_target_sheet(xls)
    df = xls[target_sheet].copy()

    ensure_string_columns(df, ["改写问题1", "改写问题2", "改写问题3"])  # ensure columns exist and are string-like

    processed = 0
    empty = 0

    for idx, val in df["条目描述"].items():
        text = "" if pd.isna(val) else str(val).strip()
        if not text:
            empty += 1
            df.at[idx, "改写问题1"] = ""
            df.at[idx, "改写问题2"] = ""
            df.at[idx, "改写问题3"] = ""
            continue
        p1, p2, p3 = gen_paraphrases(text)
        df.at[idx, "改写问题1"] = p1
        df.at[idx, "改写问题2"] = p2
        df.at[idx, "改写问题3"] = p3
        processed += 1

    xls[target_sheet] = df
    with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
        for name, sdf in xls.items():
            sdf.to_excel(writer, sheet_name=name, index=False)

    return {"processed": processed, "empty": empty, "sheet": target_sheet}


def main():
    parser = argparse.ArgumentParser(description="Rule-based QA paraphrasing (CN/EN) for Excel column '条目描述' -> 改写问题1/2/3")
    parser.add_argument("--input", required=True, help="Path to input .xlsx")
    parser.add_argument("--output", required=True, help="Path to output .xlsx")
    args = parser.parse_args()

    if not os.path.exists(args.input):
        print(f"[ERROR] Input file not found: {args.input}")
        sys.exit(1)

    os.makedirs(os.path.dirname(args.output), exist_ok=True)

    stats = process_file(args.input, args.output)
    print(f"[DONE] Sheet: {stats['sheet']} | Processed: {stats['processed']} | Empty: {stats['empty']}")
    print(f"[OUT] {args.output}")


if __name__ == "__main__":
    main()

